:root {
    --primary-color: #0a3b66;
    --secondary-color: #1a5b96;
    --accent-color: #2c7ac9;
    --text-color: #ffffff;
    --border-color: #1d4e80;
    --bg-color: #051a30;
    --item-bg-color: #0c2b4a;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Microsoft YaHei', Arial, sans-serif;
    background-color: var(--bg-color);
    color: var(--text-color);
    line-height: 1.6;
}

header {
    background-color: var(--primary-color);
    padding: 10px 20px;
    border-bottom: 1px solid var(--border-color);
}

header h1 {
    font-size: 18px;
    font-weight: normal;
}

nav ul {
    display: flex;
    list-style: none;
    margin-top: 10px;
}

nav li {
    margin-right: 20px;
}

nav a {
    color: var(--text-color);
    text-decoration: none;
    padding: 5px 10px;
}

nav li.active a {
    background-color: var(--accent-color);
    border-radius: 3px;
}

.container {
    display: flex;
    height: calc(100vh - 80px);
}

/* 旅客信息侧边栏 */
.passenger-info {
    width: 250px;
    background-color: var(--item-bg-color);
    padding: 15px;
    border-right: 1px solid var(--border-color);
}

.passenger-avatar {
    width: 100px;
    height: 100px;
    border-radius: 50%;
    background-color: #ccc;
    margin: 0 auto 15px;
    overflow: hidden;
}

.passenger-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.info-item {
    margin-bottom: 10px;
}

.info-label {
    font-size: 12px;
    color: #a0b7d0;
}

.info-value {
    font-size: 14px;
}

/* 主内容区 */
.timeline-container {
    flex: 1;
    overflow-y: auto;
    padding: 15px;
}

.filter-bar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding: 10px;
    background-color: var(--item-bg-color);
    border-radius: 4px;
}

.date-filter {
    display: flex;
    align-items: center;
}

.time-range {
    display: flex;
    align-items: center;
    margin-left: 10px;
}

.time-range input {
    background-color: var(--primary-color);
    border: 1px solid var(--border-color);
    color: var(--text-color);
    padding: 5px;
    border-radius: 3px;
}

#search-btn {
    background-color: var(--accent-color);
    color: white;
    border: none;
    padding: 8px 15px;
    border-radius: 3px;
    cursor: pointer;
}

/* 时间线样式 */
.timeline {
    position: relative;
}

.timeline::before {
    content: '';
    position: absolute;
    top: 0;
    bottom: 0;
    left: 20px;
    width: 2px;
    background-color: var(--border-color);
}

.timeline-item {
    position: relative;
    margin-bottom: 20px;
    padding-left: 50px;
}

.timeline-item::before {
    content: '';
    position: absolute;
    left: 13px;
    top: 15px;
    width: 16px;
    height: 16px;
    border-radius: 50%;
    background-color: var(--accent-color);
    z-index: 1;
}

.timeline-item-header {
    background-color: var(--secondary-color);
    padding: 10px 15px;
    border-radius: 4px 4px 0 0;
    cursor: pointer;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.timeline-item-content {
    background-color: var(--item-bg-color);
    padding: 15px;
    border-radius: 0 0 4px 4px;
    overflow: hidden;
    transition: max-height 0.3s ease;
}

.media-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 10px;
    margin-top: 10px;
}

.media-thumbnail {
    position: relative;
    height: 150px;
    background-color: #0a2540;
    border-radius: 4px;
    overflow: hidden;
    cursor: pointer;
}

.media-thumbnail img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.media-thumbnail .play-icon {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 30px;
    color: white;
    background-color: rgba(0, 0, 0, 0.5);
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.media-caption {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background-color: rgba(0, 0, 0, 0.7);
    padding: 5px;
    font-size: 12px;
}

.audio-play {
    background-color: var(--accent-color);
    color: white;
    border: none;
    padding: 5px 10px;
    border-radius: 3px;
    cursor: pointer;
    display: flex;
    align-items: center;
    font-size: 12px;
}

.audio-play i {
    margin-right: 5px;
}

/* 模态框样式 */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.8);
}

.modal-content {
    position: relative;
    margin: 5% auto;
    width: 80%;
    max-width: 1000px;
    background-color: var(--item-bg-color);
    border-radius: 5px;
    padding: 20px;
}

.close {
    position: absolute;
    top: 10px;
    right: 20px;
    font-size: 28px;
    cursor: pointer;
}

.media-container {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 400px;
}

.media-container img,
.media-container video {
    max-width: 100%;
    max-height: 80vh;
}