:root {
    --primary-color: #0a3b66;
    --secondary-color: #1a5b96;
    --accent-color: #2c7ac9;
    --text-color: #ffffff;
    --border-color: #1d4e80;
    --bg-color: #051a30;
    --item-bg-color: #0c2b4a;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Microsoft YaHei', Arial, sans-serif;
    background-color: var(--bg-color);
    color: var(--text-color);
    line-height: 1.6;
}

header {
    background-color: var(--primary-color);
    padding: 8px 20px;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.header-left {
    display: flex;
    align-items: center;
    gap: 20px;
}

header h1 {
    font-size: 16px;
    font-weight: normal;
    margin: 0;
}

nav ul {
    display: flex;
    list-style: none;
    margin: 0;
    gap: 0;
}

nav li {
    border-right: 1px solid var(--border-color);
}

nav li:last-child {
    border-right: none;
}

nav a {
    color: var(--text-color);
    text-decoration: none;
    padding: 8px 15px;
    display: block;
    font-size: 13px;
}

nav li.active a {
    background-color: var(--accent-color);
    color: white;
}

nav a:hover {
    background-color: var(--secondary-color);
}

.container {
    display: flex;
    height: calc(100vh - 80px);
}

/* 旅客信息侧边栏 */
.passenger-info {
    width: 200px;
    background-color: var(--item-bg-color);
    padding: 15px;
    border-right: 1px solid var(--border-color);
    display: flex;
    flex-direction: column;
    align-items: center;
}

.passenger-avatar {
    width: 120px;
    height: 120px;
    border-radius: 4px;
    background-color: #666;
    margin-bottom: 15px;
    overflow: hidden;
    cursor: pointer;
}

.passenger-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.passenger-details {
    width: 100%;
    margin-bottom: 15px;
}

.info-item {
    margin-bottom: 8px;
    text-align: left;
}

.info-label {
    font-size: 12px;
    color: #a0b7d0;
    display: block;
}

.info-value {
    font-size: 13px;
    color: var(--text-color);
    display: block;
    margin-top: 2px;
}

.detail-btn {
    background-color: var(--accent-color);
    color: white;
    border: none;
    padding: 8px 20px;
    border-radius: 3px;
    cursor: pointer;
    font-size: 12px;
    width: 100%;
}

/* 主内容区 */
.timeline-container {
    flex: 1;
    overflow-y: auto;
    padding: 15px;
}

.filter-bar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    padding: 12px 15px;
    background-color: var(--item-bg-color);
    border-radius: 4px;
    border: 1px solid var(--border-color);
    font-size: 13px;
}

.date-filter {
    display: flex;
    align-items: center;
    gap: 10px;
}

.date-filter span {
    color: var(--text-color);
    font-size: 13px;
}

.time-range {
    display: flex;
    align-items: center;
    gap: 8px;
}

.time-range input, #date-select, #sort-by {
    background-color: var(--primary-color);
    border: 1px solid var(--border-color);
    color: var(--text-color);
    padding: 4px 8px;
    border-radius: 3px;
    font-size: 12px;
}

.time-range span {
    color: #a0b7d0;
    font-size: 12px;
}

#search-btn {
    background-color: var(--accent-color);
    color: white;
    border: none;
    padding: 6px 12px;
    border-radius: 3px;
    cursor: pointer;
    font-size: 12px;
}

.sort-options {
    display: flex;
    align-items: center;
    gap: 5px;
}

/* 时间线样式 */
.timeline {
    position: relative;
}

.timeline::before {
    content: '';
    position: absolute;
    top: 0;
    bottom: 0;
    left: 20px;
    width: 2px;
    background-color: var(--border-color);
}

.timeline-item {
    position: relative;
    margin-bottom: 15px;
    padding-left: 50px;
}

.timeline-item::before {
    content: '';
    position: absolute;
    left: 13px;
    top: 15px;
    width: 16px;
    height: 16px;
    border-radius: 50%;
    background-color: var(--accent-color);
    z-index: 1;
}

.timeline-item-header {
    background-color: var(--secondary-color);
    padding: 12px 15px;
    border-radius: 4px 4px 0 0;
    cursor: pointer;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border: 1px solid var(--border-color);
}

.timeline-title {
    display: flex;
    align-items: center;
    font-size: 14px;
    font-weight: 500;
}

.timeline-icon {
    margin-right: 8px;
    font-size: 12px;
    color: var(--accent-color);
}

.timeline-time {
    font-size: 12px;
    color: #a0b7d0;
}

.timeline-item-content {
    background-color: var(--item-bg-color);
    padding: 15px;
    border-radius: 0 0 4px 4px;
    border: 1px solid var(--border-color);
    border-top: none;
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.3s ease;
}

.timeline-item.expanded .timeline-item-content {
    max-height: 1000px;
}

.timeline-details {
    margin-bottom: 15px;
}

.detail-row {
    display: flex;
    margin-bottom: 5px;
    font-size: 13px;
}

.detail-label {
    color: #a0b7d0;
    min-width: 80px;
}

.detail-value {
    color: var(--text-color);
}

.media-section {
    display: flex;
    gap: 15px;
    margin-top: 15px;
}

.audio-section {
    margin-bottom: 15px;
}

.media-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
    gap: 15px;
    margin-top: 15px;
}

.media-item {
    display: flex;
    flex-direction: column;
}

.media-label {
    font-size: 11px;
    color: #a0b7d0;
    margin-bottom: 5px;
    text-align: center;
}

.media-thumbnail {
    position: relative;
    height: 120px;
    background-color: #0a2540;
    border-radius: 4px;
    overflow: hidden;
    cursor: pointer;
    border: 1px solid var(--border-color);
    display: flex;
    align-items: center;
    justify-content: center;
}

.media-placeholder {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    width: 100%;
}

.box-icon {
    font-size: 24px;
    margin-bottom: 5px;
    color: var(--accent-color);
}

.play-icon {
    font-size: 24px;
    color: white;
    background-color: rgba(0, 0, 0, 0.6);
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 5px;
}

.media-caption {
    font-size: 11px;
    color: #a0b7d0;
    text-align: center;
}

.media-time {
    font-size: 10px;
    color: #7a8fa3;
    text-align: center;
    margin-top: 5px;
}

.audio-play {
    background-color: var(--accent-color);
    color: white;
    border: none;
    padding: 8px 12px;
    border-radius: 3px;
    cursor: pointer;
    display: flex;
    align-items: center;
    font-size: 12px;
    gap: 5px;
}

.audio-icon {
    font-size: 14px;
}

/* 模态框样式 */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.8);
}

.modal-content {
    position: relative;
    margin: 5% auto;
    width: 80%;
    max-width: 1000px;
    background-color: var(--item-bg-color);
    border-radius: 5px;
    padding: 20px;
}

.close {
    position: absolute;
    top: 10px;
    right: 20px;
    font-size: 28px;
    cursor: pointer;
}

.media-container {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 400px;
}

.media-container img,
.media-container video {
    max-width: 100%;
    max-height: 80vh;
}