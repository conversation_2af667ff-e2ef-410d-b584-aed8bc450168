<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>安检过程回溯系统 - 测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f0f0f0;
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-link {
            display: inline-block;
            background-color: #2c7ac9;
            color: white;
            padding: 12px 24px;
            text-decoration: none;
            border-radius: 4px;
            margin: 10px 0;
        }
        .test-link:hover {
            background-color: #1a5b96;
        }
        .feature-list {
            margin: 20px 0;
        }
        .feature-list li {
            margin: 8px 0;
            padding: 8px;
            background-color: #f8f9fa;
            border-left: 4px solid #2c7ac9;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>安检过程回溯系统</h1>
        <p>这是一个模拟机场安检过程回溯的系统，展示了旅客从值机到安检验证的完整流程。</p>
        
        <a href="index.html" class="test-link">打开主系统</a>
        
        <h2>系统功能特点：</h2>
        <ul class="feature-list">
            <li><strong>旅客信息展示</strong> - 显示旅客基本信息、航班信息和行李信息</li>
            <li><strong>时间线回溯</strong> - 按时间顺序展示各个安检环节</li>
            <li><strong>媒体资料</strong> - 包含照片、视频和音频记录</li>
            <li><strong>交互功能</strong> - 点击展开/折叠时间线项目，查看详细信息</li>
            <li><strong>多媒体查看</strong> - 支持图片和视频的全屏查看</li>
        </ul>
        
        <h2>主要环节：</h2>
        <ul class="feature-list">
            <li><strong>行李托运</strong> - 值机台照片和行李合影</li>
            <li><strong>过闸机</strong> - 人证合一验证音频</li>
            <li><strong>安检验证</strong> - 验证台照片和多个监控摄像头视频</li>
        </ul>
        
        <h2>技术实现：</h2>
        <ul class="feature-list">
            <li>纯HTML/CSS/JavaScript实现</li>
            <li>响应式设计，适配不同屏幕尺寸</li>
            <li>模块化的CSS和JavaScript代码</li>
            <li>符合现代Web标准</li>
        </ul>
    </div>
</body>
</html>
