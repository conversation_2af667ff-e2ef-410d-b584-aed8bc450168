<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>安检过程回溯系统</title>
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <header>
        <h1>安检回溯 / 行检回溯 / 详细过程数据</h1>
        <nav>
            <ul>
                <li class="active"><a href="#all">全部回溯</a></li>
                <li><a href="#passenger">仅查看旅客</a></li>
                <li><a href="#luggage">仅查看行李</a></li>
            </ul>
        </nav>
    </header>
    
    <div class="container">
        <!-- 旅客信息面板 -->
        <aside class="passenger-info">
            <!-- 旅客头像和基本信息 -->
            <!-- ... 现有代码 ... -->
        </aside>
        
        <!-- 主内容区 -->
        <main class="timeline-container">
            <div class="filter-bar">
                <div class="date-filter">
                    <span>日期:</span>
                    <select id="date-select">
                        <option value="today">今天</option>
                        <option value="yesterday">昨天</option>
                        <option value="custom">自定义...</option>
                    </select>
                    <div class="time-range">
                        <input type="datetime-local" id="start-time">
                        <span>至</span>
                        <input type="datetime-local" id="end-time">
                    </div>
                </div>
                <button id="search-btn">过程查询</button>
                <div class="sort-options">
                    <select id="sort-by">
                        <option value="time">时间</option>
                        <option value="name">姓名</option>
                    </select>
                </div>
            </div>
            
            <!-- 时间线内容 -->
            <div class="timeline">
                <!-- 各个安检环节 -->
                <!-- ... 现有代码 ... -->
            </div>
        </main>
    </div>
    
    <!-- 媒体查看器模态框 -->
    <div id="media-viewer" class="modal">
        <div class="modal-content">
            <span class="close">&times;</span>
            <div class="media-container">
                <!-- 媒体内容将通过JS动态加载 -->
            </div>
        </div>
    </div>
    
    <script src="scripts.js"></script>
</body>
</html>