document.addEventListener('DOMContentLoaded', function() {
    // 初始化日期时间选择器
    initDateTimePickers();
    
    // 绑定媒体查看器事件
    bindMediaViewerEvents();
    
    // 绑定音频播放按钮事件
    bindAudioPlayEvents();
    
    // 绑定时间线展开/折叠事件
    bindTimelineEvents();
});

function initDateTimePickers() {
    // 设置默认时间范围为当天
    const today = new Date();
    const todayStr = today.toISOString().split('T')[0];
    
    document.getElementById('start-time').value = `${todayStr}T00:00`;
    document.getElementById('end-time').value = `${todayStr}T23:59`;
    
    // 日期选择器变化时更新时间范围
    document.getElementById('date-select').addEventListener('change', function(e) {
        const value = e.target.value;
        const today = new Date();
        
        if (value === 'today') {
            const todayStr = today.toISOString().split('T')[0];
            document.getElementById('start-time').value = `${todayStr}T00:00`;
            document.getElementById('end-time').value = `${todayStr}T23:59`;
        } else if (value === 'yesterday') {
            const yesterday = new Date(today);
            yesterday.setDate(yesterday.getDate() - 1);
            const yesterdayStr = yesterday.toISOString().split('T')[0];
            document.getElementById('start-time').value = `${yesterdayStr}T00:00`;
            document.getElementById('end-time').value = `${yesterdayStr}T23:59`;
        }
    });
}

function bindMediaViewerEvents() {
    // 获取所有媒体缩略图
    const mediaThumbnails = document.querySelectorAll('.media-thumbnail');
    const modal = document.getElementById('media-viewer');
    const closeBtn = modal.querySelector('.close');
    const mediaContainer = modal.querySelector('.media-container');
    
    // 为每个缩略图添加点击事件
    mediaThumbnails.forEach(thumbnail => {
        thumbnail.addEventListener('click', function() {
            const mediaType = this.dataset.type; // 'image', 'video', 'xray'
            const mediaUrl = this.dataset.url;
            
            // 清空并添加新的媒体内容
            mediaContainer.innerHTML = '';
            
            if (mediaType === 'video') {
                const video = document.createElement('video');
                video.src = mediaUrl;
                video.controls = true;
                video.autoplay = true;
                mediaContainer.appendChild(video);
            } else {
                const img = document.createElement('img');
                img.src = mediaUrl;
                mediaContainer.appendChild(img);
            }
            
            // 显示模态框
            modal.style.display = 'block';
        });
    });
    
    // 关闭按钮事件
    closeBtn.addEventListener('click', function() {
        modal.style.display = 'none';
    });
    
    // 点击模态框外部关闭
    window.addEventListener('click', function(event) {
        if (event.target === modal) {
            modal.style.display = 'none';
        }
    });
}

function bindAudioPlayEvents() {
    // 获取所有音频播放按钮
    const audioButtons = document.querySelectorAll('.audio-play');
    
    audioButtons.forEach(button => {
        button.addEventListener('click', function() {
            const audioUrl = this.dataset.audio;
            const audio = new Audio(audioUrl);
            audio.play();
        });
    });
}

function bindTimelineEvents() {
    // 获取所有时间线项目标题
    const timelineHeaders = document.querySelectorAll('.timeline-item-header');
    
    timelineHeaders.forEach(header => {
        header.addEventListener('click', function() {
            const content = this.nextElementSibling;
            
            // 切换内容显示/隐藏
            if (content.style.maxHeight) {
                content.style.maxHeight = null;
                this.classList.remove('expanded');
            } else {
                content.style.maxHeight = content.scrollHeight + 'px';
                this.classList.add('expanded');
            }
        });
    });
}